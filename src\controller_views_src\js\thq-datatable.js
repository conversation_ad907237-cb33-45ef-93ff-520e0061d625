var globalDTOptions = {
    "deferRender": true,
    "ordering": false,
    "sDom":
        "<'card-header'" +
        "<'card-tools col-sm-6 col-xs-12' <'float-right btn-adjust' B> <'float-right' f>>" +
        "<'card-tools col-sm-6 col-xs-12 hidden-xs float-left'l>" +
        "r>" +

        "t" +

        "<'card-footer row'" +
        "<'card-tools col-sm-6 col-xs-12 hidden-xs'i>" +
        "<'card-tools col-sm-6 col-xs-12'p>" +
        ">",
    "autoWidth": true,
    "oLanguage": {
        "sSearch":
            '<div class="input-group input-group-sm">' +
            '_INPUT_' +
            '<div class="input-group-append">' +
            '<button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>' +
            '</div>' +
            '</div>',
        'sLengthMenu':
            'Menampilkan _MENU_ data',
        'sInfo': 'Menampilkan data _START_ - _END_ / ( _TOTAL_ )',
        'sInfoFiltered': '(hasil filter dari _MAX_ data)',
        'sZeroRecords': 'Tidak ada data',
        'sInfoEmpty': 'Tidak ada data',
        'sEmptyTable': 'Tidak ada data',
        'sLoadingRecords': 'Tunggu beberapa saat...',
        'sProcessing': 'Memproses...',
        'oPaginate': {
            'sFirst': '<i class="fa fa-angle-double-left fa-xs"></i>',
            'sLast': '<i class="fa fa-angle-double-right fa-xs"></i>',
            'sNext': '<i class="fa fa-angle-right fa-xs"></i>',
            'sPrevious': '<i class="fa fa-angle-left fa-xs"></i>'
        }
    }
};
