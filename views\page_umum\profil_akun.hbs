{{#section 'style'}}
<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
<link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
<link rel="stylesheet" href="https://cdn.datatables.net/2.3.5/css/dataTables.dataTables.css" />
{{/section}}


<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h2>Data Akun</h2>
                <ul class="nav navbar-right panel_toolbox">
                    <li><a class="collapse-link"><i class="fa fa-chevron-up"></i></a>
                    </li>
                </ul>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <div id="datatable_wrapper" class="dataTables_wrapper form-inline dt-bootstrap no-footer">
                    <div class="row">
                        <div class="col-sm-12">
                            <table id="datatable" class="table table-striped table-bordered dataTable no-footer"
                                role="grid" aria-describedby="datatable_info">
                                <thead>
                                    <tr role="row">
                                        <th>No</th>
                                        <th>Nama</th>
                                        <th>Username</th>
                                        <th>Hak Akses</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row panel-akun-detail" style="display: none;">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h2>Edit Akun</h2>
                <ul class="nav navbar-right panel_toolbox">
                    <li><a class="collapse-link"><i class="fa fa-chevron-up"></i></a></li>
                    <li><a class="close-link"><i class="fa fa-close"></i></a></li>
                </ul>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <br>
                <form id="form_1" class="form-horizontal form-label-left" data-parsley-validate="" novalidate="">
                    <input type="hidden" value="">
                    <div class="form-group">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Nama <span
                                class="required restrict">*</span></label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <input name="nama" type="text" class="form-control" placeholder="Nama" required=""
                                onchange="changeTitlePanel('panel_title_2', 'Edit akun: ' + this.value)" value="Mifta"
                                fdprocessedid="v88vpv">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Alamat </label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <textarea name="alamat" class="form-control" rows="3" placeholder="Alamat"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">No HP </label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <input name="nohp" type="text" class="form-control" placeholder="No HP" value=""
                                fdprocessedid="lnj4v">
                        </div>
                    </div>
                    <div class="form-group restrict">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Username <span
                                class="required restrict">*</span></label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <input id="add_username" name="username" type="text" class="form-control"
                                placeholder="Username" required=""
                                onkeyup="((el)=&gt;{ el.value = el.value.toLowerCase(); })(this)" value="mita"
                                disabled="" fdprocessedid="9z9xx">
                        </div>
                    </div>
                    <div class="form-group restrict">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Password <span
                                class="required restrict">*</span></label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <div class="input-group">
                                <input id="password" name="password" type="password" class="form-control"
                                    placeholder="Password" required="" value="827ccb0eea8a706c4c34a16891f84e7b"
                                    fdprocessedid="wswhv">
                                <span class="input-group-btn">
                                    <i class="fa fa-eye" id="togglePassword"
                                        style="height: 30px; width: auto; font-size: 25px; margin: 5px;"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Level Akses <span
                                class="required restrict">*</span></label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <select id="level_akses" name="level_akses" class="select2_single form-control"
                                tabindex="-1" required="">
                                <option value="0">Admin Super</option>
                                <option value="1" selected="selected">Admin</option>
                            </select>
                        </div>
                    </div>

                    <br>
                    <div class="form-group">
                        <label class="control-label col-md-3 col-sm-3 col-xs-12">Foto Profil</label>
                        <div class="col-md-9 col-sm-9 col-xs-12">
                            <form action="/file-upload" class="dropzone" id="my-awesome-dropzone"></form>
                        </div>
                    </div>

                    <div class="ln_solid"></div>
                    <div class="form-group restrict">
                        <div class="col-md-9 col-sm-9 col-xs-12 col-md-offset-3">
                            <button type="submit" class="btn btn-success" fdprocessedid="5g6m6f">Simpan</button>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>

{{#section 'script'}}
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script src="https://cdn.datatables.net/2.3.5/js/dataTables.js"></script>

<!-- Init Script -->
<script>
    $(function () {
        // var token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImptcyIsImV4cCI6MTc2MzUzMzY5NX0.3Bxd--jkjJQ1SeXtJJmJKOAO5J0Mch37UfAeUYAQlnbDGzaCsLuHAcS16PwLQadYMpDWXhFu4YyEm6jsMCdatQ"

        // const config = {
        //     headers: { Authorization: `Bearer ${token}` }
        // };

        // const bodyParameters = {
        //     key: "value"
        // };

        // axios.post(
        //     'https://lapak.indesc.com/api/user',
        //     bodyParameters,
        //     config
        // ).then(console.log).catch(console.log);
    }

        var data = [{
        "id": "686a4ea6c7b15b29d98c7528",
        "name": "Developer",
        "phone": "+62857 9337 5750",
        "address": "Tasikmalaya",
        "photo": "/api/bucket/68e87547bc6dc6009507a525",
        "username": "jms",
        "privilege": "Super Admin",
        "active": true
    },
    {
        "id": "686bc8ecc66b3367c4f37a0f",
        "name": "Tester",
        "photo": "/api/bucket/68e87547bc6dc6009507a525",
        "username": "tester",
        "privilege": "Admin",
        "active": true
    }]
    var dt_options = $.extend({}, globalDTOptions, {
        "data": data,
        "columns": [
            null,
            null,
            null,
            null,
            null,
            null,
        ],
        "columnDefs": [
            "defaultContent": "-",
            "targets": "_all",
            {
                "render": function (data, type, row) {
                    return '' +
                        '<button class="btn btn-warning btnAkunEdit">Edit</button>' +
                        '<button class="btn btn-dark btnAkunActiveCtrl">Nonaktifkan</button>'
                        ;
                },
                "targets": [5]
            },
        ],
        "drawCallback": function (setting) {
        }
    });

    dt = $('#datatable').DataTable(dt_options);


    });
</script>

<!-- Action Script -->
<script>
    $(function () {
        $(".btn-tambah-akun").on("click", function (e) {
            e.preventDefault();
            $(".panel-akun-detail").show();
        })
    });
</script>
{{/section}}