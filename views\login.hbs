<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!-- Meta, title, CSS, favicons, etc. -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="icon" type="image/x-icon" href="{{logo}}">
    <title>{{name}}</title>

    <!-- Bootstrap -->
    <link href="/vendors/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/vendors/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- NProgress -->
    <link href="/vendors/nprogress/nprogress.css" rel="stylesheet">
    <!-- Animate.css -->
    <link href="/vendors/animate.css/animate.min.css" rel="stylesheet">

    <!-- Custom Theme Style -->
    <link href="/build/css/custom.min.css" rel="stylesheet">
    <style>
        .logo {
            display: inline;
            width: 40px;
        }
    </style>
</head>

<body class="login">
    <div>
        <a class="hiddenanchor" id="signup"></a>
        <a class="hiddenanchor" id="signin"></a>

        <div class="login_wrapper">
            <div id="login" class="animate form login_form">
                <section class="login_content">
                    <form id="form-login">
                        <h1>Login Form</h1>
                        <div class="form-group">
                            <input name="username" type="text" class="form-control" placeholder="Username"
                                required="" />
                        </div>
                        <div class="form-group">
                            <input name="password" type="password" class="form-control" placeholder="Password"
                                required="" />
                        </div>
                        <div>
                            <button id="btn-login" class="btn btn-default submit" href="index.html">Login</button>
                            <a class="reset_pass" href="#">Lost your password?</a>
                        </div>

                        <div class="clearfix"></div>

                        <div class="separator">
                            <p class="change_link">New to site?
                                <a href="#signup" class="to_register"> Create Account </a>
                            </p>

                            <div class="clearfix"></div>
                            <br />

                            <div>
                                <h1><img class="logo" src="{{logo}}"></img> {{name}}</h1>
                                <p>©2016 All Rights Reserved. {{name}} App is a Retail Mangement Sytem. Privacy and
                                    Terms</p>
                            </div>
                        </div>
                    </form>
                </section>
            </div>

            <div id="register" class="animate form registration_form">
                <section class="login_content">
                    <form id="form-register">
                        <h1>Create Account</h1>
                        <h4>
                            Situs ini merupakan situs tertutup
                        </h4>
                        <div>
                            Silahkan mengghubungi admnistrator untuk menjadi pengguna dari situs ini
                        </div>

                        <div class="clearfix"></div>

                        <div class="separator">
                            <p class="change_link">Already a member ?
                                <a href="#signin" class="to_register"> Log in </a>
                            </p>

                            <div class="clearfix"></div>
                            <br />

                            <div>
                                <h1><img class="logo" src="{{logo}}"></img> {{name}}</h1>
                                <p>©2016 All Rights Reserved. {{name}} App is a Retail Mangement Sytem. Privacy and
                                    Terms</p>
                            </div>
                        </div>
                    </form>
                </section>
            </div>
        </div>
    </div>
</body>

</html>
<script src="/vendors/jquery/dist/jquery.min.js"></script>
<script src="/vendors/jquery-validation/jquery.validate.min.js"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>

<script type="text/javascript">
    $(document).ready(function () {

        $('#form-login').validate({
            rules: {
                email: { required: true },
                password: { required: true }
            },
            messages: {
                email: {
                    required: "Silahkan masukkan E Mail anda"
                },
                password: {
                    required: "Silahkan masukkan password anda"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function (element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            }
        });

        $('#form-login').on('submit', function (e) {
            e.preventDefault();
            goSendLogin();
        });

    });

    function goSendLogin() {
        if ($('#form-login').valid()) {
            var formData = {
                username: $('#form-login input[name="username"]').val(),
                password: $('#form-login input[name="password"]').val()
            };

            // Show loading state
            $('#btn-login').prop('disabled', true).text('Logging in...');

            axios({
                method: 'post',
                url: 'https://lapak.indesc.com/api/auth',
                data: formData
            }).then(function (response) {
                console.log('Login successful:', response);

                if (response.data && response.data.data) {
                    const loginData = response.data.data;

                    // Store authentication data in localStorage
                    localStorage.setItem('authToken', loginData.token);
                    localStorage.setItem('userData', JSON.stringify(loginData.user));

                    // Show success message
                    alert('Login berhasil! Selamat datang, ' + loginData.user.name);

                    // Redirect to dashboard
                    window.location.href = '/umum/dashboard';
                } else {
                    alert('Login gagal: Response tidak valid');
                    $('#btn-login').prop('disabled', false).text('Login');
                }
            }).catch(function (error) {
                console.error('Login error:', error);

                let errorMessage = 'Login gagal. Silakan coba lagi.';
                if (error.response && error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }

                alert(errorMessage);
                $('#btn-login').prop('disabled', false).text('Login');
            });

            
        }
    }
</script>