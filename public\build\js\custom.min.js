(t=>{jQuery.fn[t]=function(e){return e?this.bind("resize",(a=e,function(){var e=this,t=arguments;c?clearTimeout(c):i&&a.apply(e,t),c=setTimeout(function(){i||a.apply(e,t),c=null},n||100)})):this.trigger(t);var a,n,i,c}})((jQuery,"smartresize"));var CURRENT_URL=window.location.href.split("#")[0].split("?")[0],$BODY=$("body"),$MENU_TOGGLE=$("#menu_toggle"),$SIDEBAR_MENU=$("#sidebar-menu"),$SIDEBAR_FOOTER=$(".sidebar-footer"),$LEFT_COL=$(".left_col"),$RIGHT_COL=$(".right_col"),$NAV_MENU=$(".nav_menu"),$FOOTER=$("footer"),checkState=($(document).ready(function(){function a(){$RIGHT_COL.css("min-height",$(window).height());var e=$BODY.outerHeight(),t=$BODY.hasClass("footer_fixed")?-10:$FOOTER.height(),a=$LEFT_COL.eq(1).height()+$SIDEBAR_FOOTER.height(),a=e<a?a:e;a-=$NAV_MENU.height()+t,$RIGHT_COL.css("min-height",a)}$SIDEBAR_MENU.find("a").on("click",function(e){var t=$(this).parent();t.is(".active")?(t.removeClass("active active-sm"),$("ul:first",t).slideUp(function(){a()})):(t.parent().is(".child_menu")||($SIDEBAR_MENU.find("li").removeClass("active active-sm"),$SIDEBAR_MENU.find("li ul").slideUp()),t.addClass("active"),$("ul:first",t).slideDown(function(){a()}))}),$MENU_TOGGLE.on("click",function(){$BODY.hasClass("nav-md")?($SIDEBAR_MENU.find("li.active ul").hide(),$SIDEBAR_MENU.find("li.active").addClass("active-sm").removeClass("active")):($SIDEBAR_MENU.find("li.active-sm ul").show(),$SIDEBAR_MENU.find("li.active-sm").addClass("active").removeClass("active-sm")),$BODY.toggleClass("nav-md nav-sm"),a(),$(".dataTable").each(function(){$(this).dataTable().fnDraw()})}),$SIDEBAR_MENU.find('a[href="'+CURRENT_URL+'"]').parent("li").addClass("current-page"),$SIDEBAR_MENU.find("a").filter(function(){return this.href==CURRENT_URL}).parent("li").addClass("current-page").parents("ul").slideDown(function(){a()}).parent().addClass("active"),$(window).smartresize(function(){a()}),a(),$.fn.mCustomScrollbar&&$(".menu_fixed").mCustomScrollbar({autoHideScrollbar:!0,theme:"minimal",mouseWheel:{preventDefault:!0}})}),$(document).ready(function(){$(".collapse-link").on("click",function(){var e=$(this).closest(".x_panel"),t=$(this).find("i"),a=e.find(".x_content");e.attr("style")?a.slideToggle(200,function(){e.removeAttr("style")}):(a.slideToggle(200),e.css("height","auto")),t.toggleClass("fa-chevron-up fa-chevron-down")}),$(".close-link").click(function(){$(this).closest(".x_panel").remove()})}),$(document).ready(function(){$('[data-toggle="tooltip"]').tooltip({container:"body"})}),$(document).ready(function(){$(".progress .progress-bar")[0]&&$(".progress .progress-bar").progressbar()}),$(document).ready(function(){$(".js-switch")[0]&&Array.prototype.slice.call(document.querySelectorAll(".js-switch")).forEach(function(e){new Switchery(e,{color:"#26B99A"})})}),$(document).ready(function(){$("input.flat")[0]&&$(document).ready(function(){$("input.flat").iCheck({checkboxClass:"icheckbox_flat-green",radioClass:"iradio_flat-green"})})}),$("table input").on("ifChecked",function(){checkState="",$(this).parent().parent().parent().addClass("selected"),countChecked()}),$("table input").on("ifUnchecked",function(){checkState="",$(this).parent().parent().parent().removeClass("selected"),countChecked()}),"");function countChecked(){"all"===checkState&&$(".bulk_action input[name='table_records']").iCheck("check"),"none"===checkState&&$(".bulk_action input[name='table_records']").iCheck("uncheck");var e=$(".bulk_action input[name='table_records']:checked").length;e?($(".column-title").hide(),$(".bulk-actions").show(),$(".action-cnt").html(e+" Records Selected")):($(".column-title").show(),$(".bulk-actions").hide())}$(".bulk_action input").on("ifChecked",function(){checkState="",$(this).parent().parent().parent().addClass("selected"),countChecked()}),$(".bulk_action input").on("ifUnchecked",function(){checkState="",$(this).parent().parent().parent().removeClass("selected"),countChecked()}),$(".bulk_action input#check-all").on("ifChecked",function(){checkState="all",countChecked()}),$(".bulk_action input#check-all").on("ifUnchecked",function(){checkState="none",countChecked()}),$(document).ready(function(){$(".expand").on("click",function(){$(this).next().slideToggle(200),"+"==($expand=$(this).find(">:first-child")).text()?$expand.text("-"):$expand.text("+")})}),"undefined"!=typeof NProgress&&($(document).ready(function(){NProgress.start()}),$(window).on("load",function(){NProgress.done()}));var globalDTOptions={deferRender:!0,ordering:!1,sDom:"<'card-header'<'card-tools col-sm-6 col-xs-12' <'float-right btn-adjust' B> <'float-right' f>><'card-tools col-sm-6 col-xs-12 hidden-xs float-left'l>r>t<'card-footer row'<'card-tools col-sm-6 col-xs-12 hidden-xs'i><'card-tools col-sm-6 col-xs-12'p>>",autoWidth:!0,oLanguage:{sSearch:'<div class="input-group input-group-sm">_INPUT_<div class="input-group-append"><button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button></div></div>',sLengthMenu:"Menampilkan _MENU_ data",sInfo:"Menampilkan data _START_ - _END_ / ( _TOTAL_ )",sInfoFiltered:"(hasil filter dari _MAX_ data)",sZeroRecords:"Tidak ada data",sInfoEmpty:"Tidak ada data",sEmptyTable:"Tidak ada data",sLoadingRecords:"Tunggu beberapa saat...",sProcessing:"Memproses...",oPaginate:{sFirst:'<i class="fa fa-angle-double-left fa-xs"></i>',sLast:'<i class="fa fa-angle-double-right fa-xs"></i>',sNext:'<i class="fa fa-angle-right fa-xs"></i>',sPrevious:'<i class="fa fa-angle-left fa-xs"></i>'}}};